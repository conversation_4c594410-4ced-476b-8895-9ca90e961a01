"""
Audio Translator - Clean UI
Minimal design with no unnecessary placeholders.
"""

import os
import sys
import subprocess
import importlib.util
from pathlib import Path

# PySide6 imports
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QProgressBar, QFileDialog, QMessageBox,
    QListWidget, QListWidgetItem, QComboBox, QFrame
)
from PySide6.QtCore import Qt, QThread, QTimer, Signal, QSettings, QSize
from PySide6.QtGui import QFont, QDragEnterEvent, QDropEvent

# Check and install required packages
required_packages = ["torch", "whisper", "pysrt", "tqdm", "numpy", "ffmpeg-python", "moviepy"]

def install_required_packages():
    """Install required packages if not already installed."""
    for package in required_packages:
        try:
            importlib.util.find_spec(package)
        except ImportError:
            print(f"Installing {package}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])

install_required_packages()

# Import core modules
from audio_translator import AudioTranslator
from video_processor import VideoProcessor

class ThemeManager:
    """Clean minimal theme system."""

    @staticmethod
    def get_dark_theme():
        return """
        QMainWindow {
            background-color: #1a1a1a;
            color: #ffffff;
            font-family: 'Segoe UI', Arial, sans-serif;
            font-size: 13px;
        }

        QWidget {
            background-color: #1a1a1a;
            color: #ffffff;
        }

        QLabel {
            color: #ffffff;
            background: transparent;
        }

        QPushButton {
            background-color: #4da6ff;
            border: 1px solid #4da6ff;
            border-radius: 6px;
            padding: 8px 16px;
            font-size: 13px;
            font-weight: 500;
            color: #ffffff;
            min-width: 100px;
            min-height: 32px;
        }

        QPushButton:hover {
            background-color: #66b3ff;
            border-color: #66b3ff;
        }

        QPushButton:disabled {
            background-color: #404040;
            border-color: #404040;
            color: #888888;
        }

        QPushButton#primaryButton {
            background-color: #333333;
            border-color: #333333;
            color: #ffffff;
            font-size: 14px;
            font-weight: 600;
            min-width: 140px;
            min-height: 40px;
            padding: 10px 20px;
        }

        QPushButton#primaryButton:hover {
            background-color: #444444;
            border-color: #444444;
        }

        QPushButton#primaryButton:disabled {
            background-color: #333333;
            border-color: #333333;
            color: #888888;
        }

        QPushButton#primaryButtonActive {
            background-color: #4CAF50;
            border-color: #4CAF50;
            font-size: 14px;
            font-weight: 600;
            min-width: 140px;
            min-height: 40px;
            padding: 10px 20px;
        }

        QPushButton#primaryButtonActive:hover {
            background-color: #5cbf60;
            border-color: #5cbf60;
        }

        QPushButton#dangerButton {
            background-color: #f44336;
            border-color: #f44336;
            border-radius: 50%;
            min-width: 24px;
            min-height: 24px;
            max-width: 24px;
            max-height: 24px;
            padding: 0px;
            font-size: 12px;
        }

        QPushButton#dangerButton:hover {
            background-color: #f66356;
            border-color: #f66356;
        }

        QPushButton#themeToggle {
            background-color: #333333;
            border: none;
            border-radius: 16px;
            min-width: 60px;
            min-height: 32px;
            max-width: 60px;
            max-height: 32px;
            padding: 0px;
            font-size: 14px;
        }

        QPushButton#themeToggle:hover {
            background-color: #444444;
        }

        QComboBox {
            background-color: #2d2d2d;
            border: 1px solid #555555;
            border-radius: 4px;
            padding: 4px 8px;
            color: #ffffff;
            height: 28px;
            font-size: 13px;
        }

        QComboBox:hover {
            border-color: #4da6ff;
        }

        QComboBox::drop-down {
            border: none;
            width: 20px;
        }

        QComboBox::down-arrow {
            image: none;
            border-left: 4px solid transparent;
            border-right: 4px solid transparent;
            border-top: 6px solid #ffffff;
        }

        QComboBox QAbstractItemView {
            background-color: #2d2d2d;
            border: 1px solid #4da6ff;
            color: #ffffff;
            selection-background-color: #4da6ff;
        }

        QListWidget {
            background-color: #2d2d2d;
            border: 1px solid #404040;
            border-radius: 4px;
            alternate-background-color: #333333;
        }

        QListWidget::item {
            background-color: transparent;
            border: none;
            padding: 4px;
            margin: 1px;
        }

        QListWidget::item:selected {
            background-color: #4da6ff;
        }

        QProgressBar {
            border: 1px solid #404040;
            border-radius: 4px;
            text-align: center;
            background-color: #2d2d2d;
            color: #ffffff;
            font-size: 12px;
            height: 24px;
        }

        QProgressBar::chunk {
            background-color: #4CAF50;
            border-radius: 3px;
        }

        QFrame#dropArea {
            border: 2px dashed #4da6ff;
            border-radius: 6px;
            background-color: rgba(77, 166, 255, 0.05);
        }

        QFrame#dropArea:hover {
            background-color: rgba(77, 166, 255, 0.1);
        }
        """

    @staticmethod
    def get_light_theme():
        return """
        QMainWindow {
            background-color: #ffffff;
            color: #212529;
            font-family: 'Segoe UI', Arial, sans-serif;
            font-size: 13px;
        }

        QWidget {
            background-color: #ffffff;
            color: #212529;
        }

        QLabel {
            color: #212529;
            background: transparent;
        }

        QPushButton {
            background-color: #0d6efd;
            border: 1px solid #0d6efd;
            border-radius: 6px;
            padding: 8px 16px;
            font-size: 13px;
            font-weight: 500;
            color: #ffffff;
            min-width: 100px;
            min-height: 32px;
        }

        QPushButton:hover {
            background-color: #0b5ed7;
            border-color: #0b5ed7;
        }

        QPushButton:disabled {
            background-color: #e9ecef;
            border-color: #e9ecef;
            color: #6c757d;
        }

        QPushButton#primaryButton {
            background-color: #f8f9fa;
            border-color: #dee2e6;
            color: #495057;
            font-size: 14px;
            font-weight: 600;
            min-width: 140px;
            min-height: 40px;
            padding: 10px 20px;
        }

        QPushButton#primaryButton:hover {
            background-color: #e9ecef;
            border-color: #ced4da;
        }

        QPushButton#primaryButton:disabled {
            background-color: #f8f9fa;
            border-color: #dee2e6;
            color: #adb5bd;
        }

        QPushButton#primaryButtonActive {
            background-color: #198754;
            border-color: #198754;
            font-size: 14px;
            font-weight: 600;
            min-width: 140px;
            min-height: 40px;
            padding: 10px 20px;
        }

        QPushButton#primaryButtonActive:hover {
            background-color: #157347;
            border-color: #157347;
        }

        QPushButton#dangerButton {
            background-color: #dc3545;
            border-color: #dc3545;
            border-radius: 50%;
            min-width: 24px;
            min-height: 24px;
            max-width: 24px;
            max-height: 24px;
            padding: 0px;
            font-size: 12px;
        }

        QPushButton#dangerButton:hover {
            background-color: #bb2d3b;
            border-color: #bb2d3b;
        }

        QPushButton#themeToggle {
            background-color: #f8f9fa;
            border: none;
            border-radius: 16px;
            min-width: 60px;
            min-height: 32px;
            max-width: 60px;
            max-height: 32px;
            padding: 0px;
            font-size: 14px;
            color: #495057;
        }

        QPushButton#themeToggle:hover {
            background-color: #e9ecef;
        }

        QComboBox {
            background-color: #ffffff;
            border: 1px solid #ced4da;
            border-radius: 4px;
            padding: 4px 8px;
            color: #212529;
            height: 28px;
            font-size: 13px;
        }

        QComboBox:hover {
            border-color: #0d6efd;
        }

        QComboBox::drop-down {
            border: none;
            width: 20px;
        }

        QComboBox::down-arrow {
            image: none;
            border-left: 4px solid transparent;
            border-right: 4px solid transparent;
            border-top: 6px solid #495057;
        }

        QComboBox QAbstractItemView {
            background-color: #ffffff;
            border: 1px solid #0d6efd;
            color: #212529;
            selection-background-color: #0d6efd;
            selection-color: #ffffff;
        }

        QListWidget {
            background-color: #ffffff;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            alternate-background-color: #f8f9fa;
        }

        QListWidget::item {
            background-color: transparent;
            border: none;
            padding: 4px;
            margin: 1px;
        }

        QListWidget::item:selected {
            background-color: #0d6efd;
            color: #ffffff;
        }

        QProgressBar {
            border: 1px solid #dee2e6;
            border-radius: 4px;
            text-align: center;
            background-color: #f8f9fa;
            color: #212529;
            font-size: 12px;
            height: 24px;
        }

        QProgressBar::chunk {
            background-color: #198754;
            border-radius: 3px;
        }

        QFrame#dropArea {
            border: 2px dashed #0d6efd;
            border-radius: 6px;
            background-color: rgba(13, 110, 253, 0.05);
        }

        QFrame#dropArea:hover {
            background-color: rgba(13, 110, 253, 0.1);
        }
        """

class FileItem(QWidget):
    """Clean file item widget."""
    remove_requested = Signal(str)

    def __init__(self, file_path, parent=None):
        super().__init__(parent)
        self.file_path = file_path
        self.setup_ui()

    def setup_ui(self):
        layout = QHBoxLayout(self)
        layout.setContentsMargins(12, 8, 12, 8)
        layout.setSpacing(12)

        # File name
        file_name = Path(self.file_path).name
        if len(file_name) > 50:
            file_name = file_name[:47] + "..."

        self.name_label = QLabel(file_name)
        self.name_label.setStyleSheet("font-weight: 500; padding: 2px;")

        # Status
        self.status_label = QLabel("Ready")
        self.status_label.setStyleSheet("color: #888888; font-size: 12px; padding: 2px;")
        self.status_label.setFixedWidth(80)

        # Remove button - circular X
        remove_btn = QPushButton("×")
        remove_btn.setObjectName("dangerButton")
        remove_btn.clicked.connect(lambda: self.remove_requested.emit(self.file_path))

        layout.addWidget(self.name_label)
        layout.addStretch()
        layout.addWidget(self.status_label)
        layout.addWidget(remove_btn)

        self.setFixedHeight(44)

    def update_status(self, status):
        self.status_label.setText(status)

class DropArea(QFrame):
    """Clean drop area widget."""
    files_dropped = Signal(list)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setObjectName("dropArea")
        self.setAcceptDrops(True)
        self.setup_ui()

    def setup_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 16, 20, 16)
        layout.setAlignment(Qt.AlignCenter)

        text_label = QLabel("Drop audio/video files here")
        text_label.setAlignment(Qt.AlignCenter)
        text_label.setStyleSheet("color: #888888; font-size: 14px; padding: 4px;")

        layout.addWidget(text_label)
        self.setFixedHeight(70)

    def dragEnterEvent(self, event: QDragEnterEvent):
        if event.mimeData().hasUrls():
            event.acceptProposedAction()

    def dropEvent(self, event: QDropEvent):
        files = []
        for url in event.mimeData().urls():
            if url.isLocalFile():
                file_path = url.toLocalFile()
                if self.is_supported_file(file_path):
                    files.append(file_path)

        if files:
            self.files_dropped.emit(files)
        event.acceptProposedAction()

    def is_supported_file(self, file_path):
        supported_extensions = {
            '.mp3', '.wav', '.flac', '.ogg', '.m4a', '.aac',
            '.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm'
        }
        return Path(file_path).suffix.lower() in supported_extensions

class TranslationWorker(QThread):
    """Worker thread for translation."""
    progress_updated = Signal(str, float, str)
    file_complete = Signal(str, str)
    file_error = Signal(str, str)
    all_complete = Signal()

    def __init__(self, file_paths, model_size):
        super().__init__()
        self.file_paths = file_paths
        self.model_size = model_size

    def run(self):
        try:
            for i, file_path in enumerate(self.file_paths):
                try:
                    self.process_single_file(file_path, i + 1, len(self.file_paths))
                except Exception as e:
                    self.file_error.emit(file_path, str(e))
                    continue
            self.all_complete.emit()
        except Exception as e:
            for file_path in self.file_paths:
                self.file_error.emit(file_path, str(e))

    def process_single_file(self, file_path, current_index, total_files):
        file_path_obj = Path(file_path)
        is_video = self.is_video_file(file_path)

        self.progress_updated.emit(file_path, 0.0, f"Starting ({current_index}/{total_files})")

        audio_path = file_path
        if is_video:
            self.progress_updated.emit(file_path, 0.1, "Extracting audio...")
            audio_path = self.extract_audio_from_video(file_path)
            self.progress_updated.emit(file_path, 0.2, "Audio extracted")

        self.progress_updated.emit(file_path, 0.3, f"Loading {self.model_size} model...")
        translator = AudioTranslator(
            model_size=self.model_size,
            progress_callback=lambda p, s: self.progress_updated.emit(file_path, 0.3 + (p * 0.7), s)
        )

        output_path = file_path_obj.with_suffix('.srt')
        result_path = translator.translate_audio(audio_path, str(output_path))
        self.file_complete.emit(file_path, result_path)

    def extract_audio_from_video(self, video_path):
        processor = VideoProcessor(progress_callback=lambda p, s: self.progress_updated.emit(video_path, p, s))
        return processor.extract_audio(video_path)

    def is_video_file(self, file_path):
        video_extensions = {'.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm'}
        return Path(file_path).suffix.lower() in video_extensions

class AudioTranslatorApp(QMainWindow):
    def __init__(self):
        super().__init__()

        # Settings
        self.settings = QSettings("VGScript", "AudioTranslator")
        self.is_dark_theme = self.settings.value("dark_theme", True, type=bool)

        # File management
        self.file_paths = []
        self.file_widgets = {}
        self.worker = None

        # Setup UI
        self.setup_ui()
        self.apply_theme()

        # Check GPU after UI is ready
        QTimer.singleShot(500, self.check_gpu)

    def setup_ui(self):
        """Setup clean minimal UI."""
        self.setWindowTitle("Audio Translator")

        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(20, 20, 20, 70)  # 50px bottom margin + 20px padding
        main_layout.setSpacing(15)

        # Header
        self.create_header(main_layout)

        # File section
        self.create_file_section(main_layout)

        # Settings row
        self.create_settings_row(main_layout)

        # Progress section
        self.create_progress_section(main_layout)

        # Action button
        self.create_action_section(main_layout)

        # Fit window to content
        self.adjustSize()
        self.setMinimumSize(self.sizeHint())

        # Create audio folder
        if not os.path.exists('audio'):
            os.makedirs('audio')

    def create_header(self, parent_layout):
        """Create clean header."""
        header_layout = QHBoxLayout()
        header_layout.setContentsMargins(0, 0, 0, 8)

        # Title
        title_label = QLabel("Audio Translator")
        title_label.setStyleSheet("font-size: 24px; font-weight: 600; color: #4da6ff; padding: 4px;")

        # Theme toggle - left/right slider style
        self.theme_toggle = QPushButton("🌙  ☀️" if self.is_dark_theme else "☀️  🌙")
        self.theme_toggle.setObjectName("themeToggle")
        self.theme_toggle.clicked.connect(self.toggle_theme)

        header_layout.addWidget(title_label)
        header_layout.addStretch()
        header_layout.addWidget(self.theme_toggle)

        parent_layout.addLayout(header_layout)

    def create_file_section(self, parent_layout):
        """Create clean file section."""
        # Drop area
        self.drop_area = DropArea()
        self.drop_area.files_dropped.connect(self.add_files)
        parent_layout.addWidget(self.drop_area)

        # Buttons - with proper spacing
        button_layout = QHBoxLayout()
        button_layout.setContentsMargins(0, 8, 0, 8)
        button_layout.setSpacing(12)

        self.browse_button = QPushButton("Browse Files")
        self.browse_button.clicked.connect(self.browse_files)

        self.clear_button = QPushButton("Clear All")
        self.clear_button.clicked.connect(self.clear_files)

        button_layout.addWidget(self.browse_button)
        button_layout.addWidget(self.clear_button)
        button_layout.addStretch()

        parent_layout.addLayout(button_layout)

        # File list
        self.file_list = QListWidget()
        self.file_list.setFixedHeight(120)
        self.file_list.setStyleSheet("margin-top: 4px;")
        parent_layout.addWidget(self.file_list)

    def create_settings_row(self, parent_layout):
        """Create clean settings row."""
        settings_layout = QHBoxLayout()
        settings_layout.setContentsMargins(0, 8, 0, 8)
        settings_layout.setSpacing(12)

        # Model selection
        model_label = QLabel("Model:")
        model_label.setStyleSheet("font-weight: 500; padding: 4px;")

        self.model_combo = QComboBox()
        models = [
            ("tiny", "Tiny"),
            ("base", "Base"),
            ("small", "Small"),
            ("medium", "Medium"),
            ("large", "Large")
        ]

        for value, display in models:
            self.model_combo.addItem(display, value)

        self.model_combo.setCurrentIndex(3)  # Default to medium

        # GPU status
        self.gpu_label = QLabel("Checking GPU...")
        self.gpu_label.setStyleSheet("color: #888888; font-size: 12px; padding: 4px;")

        settings_layout.addWidget(model_label)
        settings_layout.addWidget(self.model_combo)
        settings_layout.addStretch()
        settings_layout.addWidget(self.gpu_label)

        parent_layout.addLayout(settings_layout)

    def create_progress_section(self, parent_layout):
        """Create clean progress section."""
        # Progress bars
        progress_layout = QVBoxLayout()
        progress_layout.setContentsMargins(0, 12, 0, 12)
        progress_layout.setSpacing(16)  # Increased spacing between progress bars

        # Overall progress
        overall_layout = QHBoxLayout()
        overall_layout.setSpacing(12)
        overall_label = QLabel("Overall:")
        overall_label.setFixedWidth(60)
        overall_label.setFixedHeight(30)  # Fixed height to prevent cropping
        overall_label.setStyleSheet("font-weight: 500; padding: 4px;")
        overall_label.setAlignment(Qt.AlignVCenter)

        self.overall_progress = QProgressBar()
        self.overall_progress.setMinimum(0)
        self.overall_progress.setMaximum(100)

        overall_layout.addWidget(overall_label)
        overall_layout.addWidget(self.overall_progress)

        # Current progress
        current_layout = QHBoxLayout()
        current_layout.setSpacing(12)
        current_label = QLabel("Current:")
        current_label.setFixedWidth(60)
        current_label.setFixedHeight(30)  # Fixed height to prevent cropping
        current_label.setStyleSheet("font-weight: 500; padding: 4px;")
        current_label.setAlignment(Qt.AlignVCenter)

        self.current_progress = QProgressBar()
        self.current_progress.setMinimum(0)
        self.current_progress.setMaximum(100)

        current_layout.addWidget(current_label)
        current_layout.addWidget(self.current_progress)

        # Status - with proper height and spacing
        self.status_label = QLabel("Ready to translate")
        self.status_label.setStyleSheet("""
            color: #888888;
            font-size: 12px;
            padding: 8px;
            margin-top: 8px;
            background: transparent;
        """)
        self.status_label.setWordWrap(True)
        self.status_label.setMinimumHeight(50)  # Increased height to prevent cropping
        self.status_label.setAlignment(Qt.AlignTop)

        progress_layout.addLayout(overall_layout)
        progress_layout.addLayout(current_layout)
        progress_layout.addWidget(self.status_label)

        parent_layout.addLayout(progress_layout)

    def create_action_section(self, parent_layout):
        """Create clean action section."""
        action_layout = QHBoxLayout()
        action_layout.setContentsMargins(0, 12, 0, 0)
        action_layout.setSpacing(12)

        # Files info
        self.files_info_label = QLabel("No files selected")
        self.files_info_label.setStyleSheet("color: #888888; padding: 4px;")

        # Translate button
        self.translate_button = QPushButton("Start Translation")
        self.translate_button.setObjectName("primaryButton")
        self.translate_button.clicked.connect(self.start_translation)
        self.translate_button.setEnabled(False)

        action_layout.addWidget(self.files_info_label)
        action_layout.addStretch()
        action_layout.addWidget(self.translate_button)

        parent_layout.addLayout(action_layout)

    def apply_theme(self):
        """Apply the selected theme."""
        if self.is_dark_theme:
            self.setStyleSheet(ThemeManager.get_dark_theme())
        else:
            self.setStyleSheet(ThemeManager.get_light_theme())

    def toggle_theme(self):
        """Toggle between dark and light theme."""
        self.is_dark_theme = not self.is_dark_theme
        self.settings.setValue("dark_theme", self.is_dark_theme)

        # Update toggle button text
        self.theme_toggle.setText("🌙  ☀️" if self.is_dark_theme else "☀️  🌙")

        # Apply new theme
        self.apply_theme()

    def browse_files(self):
        """Browse for files."""
        audio_ext = "*.mp3 *.wav *.flac *.ogg *.m4a *.aac"
        video_ext = "*.mp4 *.avi *.mkv *.mov *.wmv *.flv *.webm"
        file_filter = f"Media Files ({audio_ext} {video_ext});;All Files (*.*)"

        filenames, _ = QFileDialog.getOpenFileNames(
            self, "Select Files", os.path.abspath("."), file_filter
        )

        if filenames:
            self.add_files(filenames)

    def add_files(self, file_paths):
        """Add files to the list."""
        for file_path in file_paths:
            if file_path not in self.file_paths:
                self.file_paths.append(file_path)
                self.add_file_widget(file_path)
        self.update_files_info()

    def add_file_widget(self, file_path):
        """Add file widget to list."""
        file_item = FileItem(file_path)
        file_item.remove_requested.connect(self.remove_file)

        list_item = QListWidgetItem()
        list_item.setSizeHint(QSize(0, 48))

        self.file_list.addItem(list_item)
        self.file_list.setItemWidget(list_item, file_item)

        self.file_widgets[file_path] = (list_item, file_item)

    def remove_file(self, file_path):
        """Remove file from list."""
        if file_path in self.file_paths:
            self.file_paths.remove(file_path)

            if file_path in self.file_widgets:
                list_item, _ = self.file_widgets[file_path]
                row = self.file_list.row(list_item)
                self.file_list.takeItem(row)
                del self.file_widgets[file_path]

        self.update_files_info()

    def clear_files(self):
        """Clear all files."""
        self.file_paths.clear()
        self.file_widgets.clear()
        self.file_list.clear()
        self.update_files_info()

    def update_files_info(self):
        """Update files info label and button state."""
        count = len(self.file_paths)
        if count == 0:
            self.files_info_label.setText("No files selected")
            self.translate_button.setEnabled(False)
            self.translate_button.setObjectName("primaryButton")
        elif count == 1:
            self.files_info_label.setText("1 file ready for translation")
            self.translate_button.setEnabled(True)
            self.translate_button.setObjectName("primaryButtonActive")
        else:
            self.files_info_label.setText(f"{count} files ready for translation")
            self.translate_button.setEnabled(True)
            self.translate_button.setObjectName("primaryButtonActive")

        # Reapply style to update button color
        self.translate_button.style().unpolish(self.translate_button)
        self.translate_button.style().polish(self.translate_button)

    def get_selected_model(self):
        """Get selected model."""
        return self.model_combo.currentData()

    def check_gpu(self):
        """Check GPU availability."""
        try:
            import torch

            if torch.cuda.is_available():
                gpu_name = torch.cuda.get_device_name(0)
                self.gpu_label.setText(f"GPU: {gpu_name}")
                self.gpu_label.setStyleSheet("color: #4CAF50; font-size: 12px;")
            else:
                self.gpu_label.setText("Using CPU (slower)")
                self.gpu_label.setStyleSheet("color: #ff9800; font-size: 12px;")

        except Exception:
            self.gpu_label.setText("GPU Error")
            self.gpu_label.setStyleSheet("color: #f44336; font-size: 12px;")

    def start_translation(self):
        """Start translation process."""
        if not self.file_paths:
            QMessageBox.warning(self, "Warning", "Please select files first.")
            return

        # Disable UI
        self.translate_button.setEnabled(False)
        self.browse_button.setEnabled(False)
        self.clear_button.setEnabled(False)

        # Reset progress
        self.overall_progress.setValue(0)
        self.current_progress.setValue(0)
        self.status_label.setText("Starting translation...")

        # Reset file statuses
        for _, (_, file_item) in self.file_widgets.items():
            file_item.update_status("Waiting")

        # Start worker
        model_size = self.get_selected_model()
        self.worker = TranslationWorker(self.file_paths.copy(), model_size)
        self.worker.progress_updated.connect(self.on_file_progress)
        self.worker.file_complete.connect(self.on_file_complete)
        self.worker.file_error.connect(self.on_file_error)
        self.worker.all_complete.connect(self.on_all_complete)
        self.worker.start()

    def on_file_progress(self, file_path, progress, status):
        """Handle file progress."""
        # Update current progress
        self.current_progress.setValue(int(progress * 100))

        # Update file status
        if file_path in self.file_widgets:
            _, file_item = self.file_widgets[file_path]
            file_item.update_status(f"{int(progress * 100)}%")

        # Update status
        file_name = Path(file_path).name
        if len(file_name) > 25:
            file_name = file_name[:22] + "..."
        self.status_label.setText(f"Processing: {file_name}\n{status}")

        # Update overall progress
        completed = sum(1 for _, (_, fi) in self.file_widgets.items()
                       if fi.status_label.text() in ["Complete", "Error"])
        overall = (completed + progress) / len(self.file_paths)
        self.overall_progress.setValue(int(overall * 100))

    def on_file_complete(self, file_path, output_path):
        """Handle file completion."""
        if file_path in self.file_widgets:
            _, file_item = self.file_widgets[file_path]
            file_item.update_status("Complete")

    def on_file_error(self, file_path, error_message):
        """Handle file error."""
        if file_path in self.file_widgets:
            _, file_item = self.file_widgets[file_path]
            file_item.update_status("Error")

    def on_all_complete(self):
        """Handle all files complete."""
        # Re-enable UI
        self.translate_button.setEnabled(True)
        self.browse_button.setEnabled(True)
        self.clear_button.setEnabled(True)

        # Update progress
        self.overall_progress.setValue(100)
        self.current_progress.setValue(100)
        self.status_label.setText("All translations completed!")

        # Count results
        completed = sum(1 for _, (_, fi) in self.file_widgets.items()
                       if fi.status_label.text() == "Complete")
        errors = sum(1 for _, (_, fi) in self.file_widgets.items()
                    if fi.status_label.text() == "Error")

        # Show message
        if errors == 0:
            QMessageBox.information(self, "Success",
                                  f"All {completed} files translated successfully!\n"
                                  f"SRT files saved in source folders.")
        else:
            QMessageBox.warning(self, "Completed with Errors",
                               f"Completed: {completed}\nErrors: {errors}")

def main():
    """Main function."""
    app = QApplication(sys.argv)
    app.setApplicationName("Audio Translator")
    app.setStyle('Fusion')

    window = AudioTranslatorApp()
    window.show()

    sys.exit(app.exec())

if __name__ == "__main__":
    main()
