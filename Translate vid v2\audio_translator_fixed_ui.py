"""
Audio Translator - Fixed UI
Non-stretchable, everything visible on launch, theme toggle, improved dropdowns.
"""

import os
import sys
import subprocess
import importlib.util
from pathlib import Path

# PySide6 imports
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QProgressBar, QFileDialog, QMessageBox, QFrame,
    QGroupBox, QListWidget, QListWidgetItem, QComboBox, QSizePolicy
)
from PySide6.QtCore import Qt, QThread, QTimer, Signal, QSettings, QSize
from PySide6.QtGui import QFont, QDragEnterEvent, QDropEvent

# Check and install required packages
required_packages = ["torch", "whisper", "pysrt", "tqdm", "numpy", "ffmpeg-python", "moviepy"]

def install_required_packages():
    """Install required packages if not already installed."""
    for package in required_packages:
        try:
            importlib.util.find_spec(package)
        except ImportError:
            print(f"Installing {package}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])

install_required_packages()

# Import core modules
from audio_translator import AudioTranslator
from video_processor import VideoProcessor

class ThemeManager:
    """Fixed theme system with improved dropdowns."""
    
    @staticmethod
    def get_dark_theme():
        return """
        QMainWindow {
            background-color: #1e1e1e;
            color: #ffffff;
            font-family: 'Segoe UI', Arial, sans-serif;
            font-size: 14px;
        }
        
        QWidget {
            background-color: #1e1e1e;
            color: #ffffff;
            font-size: 14px;
        }
        
        QGroupBox {
            font-weight: bold;
            font-size: 14px;
            border: 2px solid #404040;
            border-radius: 6px;
            margin: 8px 0px;
            padding: 15px 10px 10px 10px;
            background-color: #2d2d2d;
            color: #ffffff;
        }
        
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 15px;
            padding: 5px 10px;
            color: #4da6ff;
            background-color: #2d2d2d;
        }
        
        QLabel {
            color: #ffffff;
            padding: 5px;
            margin: 2px;
        }
        
        QPushButton {
            background-color: #4da6ff;
            border: none;
            border-radius: 6px;
            padding: 10px 18px;
            font-size: 14px;
            font-weight: bold;
            color: #ffffff;
            margin: 5px;
            min-width: 100px;
            min-height: 35px;
        }
        
        QPushButton:hover {
            background-color: #66b3ff;
        }
        
        QPushButton:disabled {
            background-color: #555555;
            color: #888888;
        }
        
        QPushButton#primaryButton {
            background-color: #4CAF50;
            padding: 12px 20px;
            font-size: 16px;
            min-width: 180px;
            min-height: 45px;
        }
        
        QPushButton#primaryButton:hover {
            background-color: #5cbf60;
        }
        
        QPushButton#dangerButton {
            background-color: #f44336;
            min-width: 80px;
        }
        
        QPushButton#dangerButton:hover {
            background-color: #f66356;
        }
        
        QPushButton#toggleButton {
            background-color: #6c757d;
            border: 2px solid #495057;
            min-width: 120px;
            min-height: 40px;
        }
        
        QPushButton#toggleButton:hover {
            background-color: #5a6268;
            border-color: #4da6ff;
        }
        
        QPushButton#toggleButton:checked {
            background-color: #4da6ff;
            border-color: #66b3ff;
        }
        
        QComboBox {
            background-color: #404040;
            border: 2px solid #555555;
            border-radius: 8px;
            padding: 8px 12px;
            color: #ffffff;
            margin: 5px;
            min-width: 200px;
            min-height: 35px;
            font-size: 14px;
            font-weight: bold;
        }
        
        QComboBox:hover {
            border-color: #4da6ff;
            background-color: #4a4a4a;
        }
        
        QComboBox:focus {
            border-color: #66b3ff;
            background-color: #4a4a4a;
        }
        
        QComboBox::drop-down {
            subcontrol-origin: padding;
            subcontrol-position: top right;
            width: 30px;
            border-left: 2px solid #555555;
            border-top-right-radius: 6px;
            border-bottom-right-radius: 6px;
            background-color: #555555;
        }
        
        QComboBox::drop-down:hover {
            background-color: #4da6ff;
            border-left-color: #4da6ff;
        }
        
        QComboBox::down-arrow {
            image: none;
            border-left: 6px solid transparent;
            border-right: 6px solid transparent;
            border-top: 8px solid #ffffff;
            margin: 0px;
        }
        
        QComboBox::down-arrow:hover {
            border-top-color: #ffffff;
        }
        
        QComboBox QAbstractItemView {
            background-color: #404040;
            border: 2px solid #4da6ff;
            border-radius: 6px;
            color: #ffffff;
            selection-background-color: #4da6ff;
            selection-color: #ffffff;
            font-size: 14px;
            font-weight: bold;
            padding: 5px;
            outline: none;
        }
        
        QComboBox QAbstractItemView::item {
            background-color: #404040;
            color: #ffffff;
            padding: 8px 12px;
            margin: 2px;
            border-radius: 4px;
            min-height: 25px;
        }
        
        QComboBox QAbstractItemView::item:hover {
            background-color: #4da6ff;
            color: #ffffff;
        }
        
        QComboBox QAbstractItemView::item:selected {
            background-color: #66b3ff;
            color: #ffffff;
        }
        
        QListWidget {
            background-color: #2d2d2d;
            border: 2px solid #404040;
            border-radius: 8px;
            padding: 10px;
            margin: 5px;
        }
        
        QListWidget::item {
            background-color: #404040;
            border: 1px solid #555555;
            border-radius: 6px;
            margin: 2px;
            padding: 10px;
        }
        
        QListWidget::item:selected {
            background-color: #4da6ff;
        }
        
        QProgressBar {
            border: 2px solid #404040;
            border-radius: 8px;
            text-align: center;
            background-color: #2d2d2d;
            color: #ffffff;
            font-weight: bold;
            font-size: 14px;
            margin: 5px;
            padding: 3px;
            min-height: 30px;
        }
        
        QProgressBar::chunk {
            background-color: #4CAF50;
            border-radius: 6px;
        }
        
        QFrame#dropZone {
            border: 3px dashed #4da6ff;
            border-radius: 10px;
            background-color: rgba(77, 166, 255, 0.1);
            margin: 10px;
            padding: 20px;
        }
        
        QFrame#dropZone:hover {
            background-color: rgba(77, 166, 255, 0.2);
            border-color: #66b3ff;
        }
        """

    @staticmethod
    def get_light_theme():
        return """
        QMainWindow {
            background-color: #f8f9fa;
            color: #212529;
            font-family: 'Segoe UI', Arial, sans-serif;
            font-size: 14px;
        }

        QWidget {
            background-color: #f8f9fa;
            color: #212529;
            font-size: 14px;
        }

        QGroupBox {
            font-weight: bold;
            font-size: 14px;
            border: 2px solid #dee2e6;
            border-radius: 6px;
            margin: 8px 0px;
            padding: 15px 10px 10px 10px;
            background-color: #ffffff;
            color: #212529;
        }

        QGroupBox::title {
            subcontrol-origin: margin;
            left: 15px;
            padding: 5px 10px;
            color: #0d6efd;
            background-color: #ffffff;
        }

        QLabel {
            color: #212529;
            padding: 5px;
            margin: 2px;
        }

        QPushButton {
            background-color: #0d6efd;
            border: none;
            border-radius: 6px;
            padding: 10px 18px;
            font-size: 14px;
            font-weight: bold;
            color: #ffffff;
            margin: 5px;
            min-width: 100px;
            min-height: 35px;
        }

        QPushButton:hover {
            background-color: #0b5ed7;
        }

        QPushButton:disabled {
            background-color: #e9ecef;
            color: #6c757d;
        }

        QPushButton#primaryButton {
            background-color: #198754;
            padding: 12px 20px;
            font-size: 16px;
            min-width: 180px;
            min-height: 45px;
        }

        QPushButton#primaryButton:hover {
            background-color: #157347;
        }

        QPushButton#dangerButton {
            background-color: #dc3545;
            min-width: 80px;
        }

        QPushButton#dangerButton:hover {
            background-color: #bb2d3b;
        }

        QPushButton#toggleButton {
            background-color: #e9ecef;
            border: 2px solid #ced4da;
            color: #495057;
            min-width: 120px;
            min-height: 40px;
        }

        QPushButton#toggleButton:hover {
            background-color: #f8f9fa;
            border-color: #0d6efd;
        }

        QPushButton#toggleButton:checked {
            background-color: #0d6efd;
            border-color: #0b5ed7;
            color: #ffffff;
        }

        QComboBox {
            background-color: #ffffff;
            border: 2px solid #ced4da;
            border-radius: 8px;
            padding: 8px 12px;
            color: #212529;
            margin: 5px;
            min-width: 200px;
            min-height: 35px;
            font-size: 14px;
            font-weight: bold;
        }

        QComboBox:hover {
            border-color: #0d6efd;
            background-color: #f8f9fa;
        }

        QComboBox:focus {
            border-color: #0b5ed7;
            background-color: #f8f9fa;
        }

        QComboBox::drop-down {
            subcontrol-origin: padding;
            subcontrol-position: top right;
            width: 30px;
            border-left: 2px solid #ced4da;
            border-top-right-radius: 6px;
            border-bottom-right-radius: 6px;
            background-color: #e9ecef;
        }

        QComboBox::drop-down:hover {
            background-color: #0d6efd;
            border-left-color: #0d6efd;
        }

        QComboBox::down-arrow {
            image: none;
            border-left: 6px solid transparent;
            border-right: 6px solid transparent;
            border-top: 8px solid #495057;
            margin: 0px;
        }

        QComboBox::down-arrow:hover {
            border-top-color: #ffffff;
        }

        QComboBox QAbstractItemView {
            background-color: #ffffff;
            border: 2px solid #0d6efd;
            border-radius: 6px;
            color: #212529;
            selection-background-color: #0d6efd;
            selection-color: #ffffff;
            font-size: 14px;
            font-weight: bold;
            padding: 5px;
            outline: none;
        }

        QComboBox QAbstractItemView::item {
            background-color: #ffffff;
            color: #212529;
            padding: 8px 12px;
            margin: 2px;
            border-radius: 4px;
            min-height: 25px;
        }

        QComboBox QAbstractItemView::item:hover {
            background-color: #0d6efd;
            color: #ffffff;
        }

        QComboBox QAbstractItemView::item:selected {
            background-color: #0b5ed7;
            color: #ffffff;
        }

        QListWidget {
            background-color: #ffffff;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            padding: 10px;
            margin: 5px;
        }

        QListWidget::item {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            margin: 2px;
            padding: 10px;
        }

        QListWidget::item:selected {
            background-color: #0d6efd;
            color: #ffffff;
        }

        QProgressBar {
            border: 2px solid #dee2e6;
            border-radius: 8px;
            text-align: center;
            background-color: #ffffff;
            color: #212529;
            font-weight: bold;
            font-size: 14px;
            margin: 5px;
            padding: 3px;
            min-height: 30px;
        }

        QProgressBar::chunk {
            background-color: #198754;
            border-radius: 6px;
        }

        QFrame#dropZone {
            border: 3px dashed #0d6efd;
            border-radius: 10px;
            background-color: rgba(13, 110, 253, 0.1);
            margin: 10px;
            padding: 20px;
        }

        QFrame#dropZone:hover {
            background-color: rgba(13, 110, 253, 0.2);
            border-color: #0b5ed7;
        }
        """

class FileItem(QWidget):
    """Fixed file item - non-stretchable."""
    remove_requested = Signal(str)

    def __init__(self, file_path, parent=None):
        super().__init__(parent)
        self.file_path = file_path
        self.setup_ui()

    def setup_ui(self):
        # Compact layout - no stretching
        layout = QHBoxLayout(self)
        layout.setContentsMargins(8, 5, 8, 5)
        layout.setSpacing(8)

        # File icon - compact
        icon_label = QLabel("🎥" if self.is_video_file() else "🎵")
        icon_label.setFixedSize(20, 20)
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setStyleSheet("font-size: 12px; margin: 0px; padding: 0px;")

        # File name - compact, no stretching
        file_name = Path(self.file_path).name
        if len(file_name) > 40:
            file_name = file_name[:37] + "..."

        name_label = QLabel(file_name)
        name_label.setStyleSheet("font-weight: bold; font-size: 11px; margin: 0px; padding: 1px;")
        name_label.setFixedWidth(320)  # Compact width
        name_label.setWordWrap(False)

        # Status - compact
        self.status_label = QLabel("Ready")
        self.status_label.setStyleSheet("font-size: 10px; font-weight: bold; margin: 0px; padding: 2px;")
        self.status_label.setFixedSize(60, 20)
        self.status_label.setAlignment(Qt.AlignCenter)

        # Remove button - compact
        remove_btn = QPushButton("×")
        remove_btn.setObjectName("dangerButton")
        remove_btn.setFixedSize(22, 22)
        remove_btn.setStyleSheet("font-size: 10px; min-width: 22px; margin: 0px; padding: 0px;")
        remove_btn.clicked.connect(lambda: self.remove_requested.emit(self.file_path))

        # Add widgets with no stretch
        layout.addWidget(icon_label)
        layout.addWidget(name_label)
        layout.addWidget(self.status_label)
        layout.addWidget(remove_btn)
        layout.addStretch(0)  # No stretch

        # Compact height
        self.setFixedHeight(35)

    def is_video_file(self):
        video_extensions = {'.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm'}
        return Path(self.file_path).suffix.lower() in video_extensions

    def update_status(self, status):
        self.status_label.setText(status)

class DropZone(QFrame):
    """Fixed drop zone - non-stretchable."""
    files_dropped = Signal(list)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setObjectName("dropZone")
        self.setAcceptDrops(True)
        self.setup_ui()

    def setup_ui(self):
        # Compact layout
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(8)
        layout.setAlignment(Qt.AlignCenter)

        # Compact icon
        icon_label = QLabel("📁")
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setStyleSheet("font-size: 30px; margin: 0px; padding: 5px;")
        icon_label.setFixedHeight(40)

        # Compact text
        text_label = QLabel("Drop files here or click Browse")
        text_label.setAlignment(Qt.AlignCenter)
        text_label.setStyleSheet("font-size: 13px; font-weight: bold; margin: 0px; padding: 3px;")
        text_label.setFixedHeight(25)

        layout.addWidget(icon_label)
        layout.addWidget(text_label)

        # Compact height
        self.setFixedHeight(90)

    def dragEnterEvent(self, event: QDragEnterEvent):
        if event.mimeData().hasUrls():
            event.acceptProposedAction()

    def dropEvent(self, event: QDropEvent):
        files = []
        for url in event.mimeData().urls():
            if url.isLocalFile():
                file_path = url.toLocalFile()
                if self.is_supported_file(file_path):
                    files.append(file_path)

        if files:
            self.files_dropped.emit(files)
        event.acceptProposedAction()

    def is_supported_file(self, file_path):
        supported_extensions = {
            '.mp3', '.wav', '.flac', '.ogg', '.m4a', '.aac',
            '.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm'
        }
        return Path(file_path).suffix.lower() in supported_extensions

class TranslationWorker(QThread):
    """Worker thread for translation."""
    progress_updated = Signal(str, float, str)
    file_complete = Signal(str, str)
    file_error = Signal(str, str)
    all_complete = Signal()

    def __init__(self, file_paths, model_size):
        super().__init__()
        self.file_paths = file_paths
        self.model_size = model_size

    def run(self):
        try:
            for i, file_path in enumerate(self.file_paths):
                try:
                    self.process_single_file(file_path, i + 1, len(self.file_paths))
                except Exception as e:
                    self.file_error.emit(file_path, str(e))
                    continue
            self.all_complete.emit()
        except Exception as e:
            for file_path in self.file_paths:
                self.file_error.emit(file_path, str(e))

    def process_single_file(self, file_path, current_index, total_files):
        file_path_obj = Path(file_path)
        is_video = self.is_video_file(file_path)

        self.progress_updated.emit(file_path, 0.0, f"Starting ({current_index}/{total_files})")

        audio_path = file_path
        if is_video:
            self.progress_updated.emit(file_path, 0.1, "Extracting audio...")
            audio_path = self.extract_audio_from_video(file_path)
            self.progress_updated.emit(file_path, 0.2, "Audio extracted")

        self.progress_updated.emit(file_path, 0.3, f"Loading {self.model_size} model...")
        translator = AudioTranslator(
            model_size=self.model_size,
            progress_callback=lambda p, s: self.progress_updated.emit(file_path, 0.3 + (p * 0.7), s)
        )

        output_path = file_path_obj.with_suffix('.srt')
        result_path = translator.translate_audio(audio_path, str(output_path))
        self.file_complete.emit(file_path, result_path)

    def extract_audio_from_video(self, video_path):
        processor = VideoProcessor(progress_callback=lambda p, s: self.progress_updated.emit(video_path, p, s))
        return processor.extract_audio(video_path)

    def is_video_file(self, file_path):
        video_extensions = {'.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm'}
        return Path(file_path).suffix.lower() in video_extensions

class AudioTranslatorApp(QMainWindow):
    def __init__(self):
        super().__init__()

        # Settings
        self.settings = QSettings("VGScript", "AudioTranslator")
        self.is_dark_theme = self.settings.value("dark_theme", True, type=bool)

        # File management
        self.file_paths = []
        self.file_widgets = {}
        self.worker = None

        # Setup UI
        self.setup_ui()
        self.apply_theme()

        # Check GPU after UI is ready
        QTimer.singleShot(500, self.check_gpu)

    def setup_ui(self):
        """Setup compact UI - everything visible on launch, non-stretchable."""
        self.setWindowTitle("Audio Translator")

        # Compact window size - everything visible
        self.setFixedSize(900, 650)  # More compact size

        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Compact main layout
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(10)

        # Create sections with compact sizes
        self.create_header(main_layout)
        self.create_file_section(main_layout)
        self.create_settings_section(main_layout)
        self.create_progress_section(main_layout)
        self.create_button_section(main_layout)

        # Create audio folder
        if not os.path.exists('audio'):
            os.makedirs('audio')

    def create_header(self, parent_layout):
        """Create compact header with theme toggle."""
        header_widget = QWidget()
        header_widget.setFixedHeight(60)  # More compact
        header_layout = QHBoxLayout(header_widget)
        header_layout.setContentsMargins(0, 0, 0, 0)
        header_layout.setSpacing(15)

        # Title section - more compact
        title_label = QLabel("Audio Translator")
        title_label.setStyleSheet("font-size: 20px; font-weight: bold; color: #4da6ff; margin: 0px; padding: 0px;")
        title_label.setFixedHeight(30)

        subtitle_label = QLabel("Japanese to English Subtitle Generator")
        subtitle_label.setStyleSheet("font-size: 11px; color: #888888; margin: 0px; padding: 0px;")
        subtitle_label.setFixedHeight(20)

        # Theme toggle - compact
        theme_label = QLabel("Theme:")
        theme_label.setStyleSheet("font-weight: bold; font-size: 12px; margin: 0px; padding: 0px;")
        theme_label.setFixedWidth(50)

        # Theme toggle button - more compact
        self.theme_toggle = QPushButton("🌙 Dark" if self.is_dark_theme else "☀️ Light")
        self.theme_toggle.setObjectName("toggleButton")
        self.theme_toggle.setCheckable(True)
        self.theme_toggle.setChecked(self.is_dark_theme)
        self.theme_toggle.clicked.connect(self.toggle_theme)
        self.theme_toggle.setFixedSize(120, 35)

        # Arrange in single row
        header_layout.addWidget(title_label)
        header_layout.addWidget(subtitle_label)
        header_layout.addStretch()
        header_layout.addWidget(theme_label)
        header_layout.addWidget(self.theme_toggle)

        parent_layout.addWidget(header_widget)

    def create_file_section(self, parent_layout):
        """Create compact file section."""
        file_group = QGroupBox("Files")
        file_group.setFixedHeight(220)  # More compact
        file_layout = QVBoxLayout(file_group)
        file_layout.setSpacing(8)

        # Compact drop zone
        self.drop_zone = DropZone()
        self.drop_zone.files_dropped.connect(self.add_files)
        file_layout.addWidget(self.drop_zone)

        # Compact button row
        button_widget = QWidget()
        button_widget.setFixedHeight(40)
        button_layout = QHBoxLayout(button_widget)
        button_layout.setContentsMargins(0, 0, 0, 0)
        button_layout.setSpacing(10)

        self.browse_button = QPushButton("Browse Files")
        self.browse_button.clicked.connect(self.browse_files)
        self.browse_button.setFixedSize(110, 30)

        self.clear_button = QPushButton("Clear All")
        self.clear_button.setObjectName("dangerButton")
        self.clear_button.clicked.connect(self.clear_files)
        self.clear_button.setFixedSize(90, 30)

        button_layout.addWidget(self.browse_button)
        button_layout.addWidget(self.clear_button)
        button_layout.addStretch()

        file_layout.addWidget(button_widget)

        # Compact file list
        self.file_list = QListWidget()
        self.file_list.setFixedHeight(70)  # More compact
        file_layout.addWidget(self.file_list)

        parent_layout.addWidget(file_group)

    def create_settings_section(self, parent_layout):
        """Create compact settings section with improved dropdown."""
        settings_group = QGroupBox("Settings")
        settings_group.setFixedHeight(100)  # More compact
        settings_layout = QHBoxLayout(settings_group)
        settings_layout.setSpacing(15)

        # Model selection - compact
        model_label = QLabel("Model:")
        model_label.setStyleSheet("font-weight: bold; font-size: 12px; margin: 0px; padding: 0px;")
        model_label.setFixedWidth(50)

        self.model_combo = QComboBox()
        models = [
            ("tiny", "Tiny - Fastest"),
            ("base", "Base - Fast"),
            ("small", "Small - Balanced"),
            ("medium", "Medium - Recommended"),
            ("large", "Large - Best Quality")
        ]

        for value, display in models:
            self.model_combo.addItem(display, value)

        self.model_combo.setCurrentIndex(3)  # Default to medium
        self.model_combo.setFixedSize(280, 35)

        # GPU info - compact
        self.gpu_info = QLabel("Checking GPU...")
        self.gpu_info.setStyleSheet("""
            background-color: #404040;
            border: 2px solid #555555;
            border-radius: 6px;
            padding: 8px;
            font-size: 11px;
            font-weight: bold;
            margin: 0px;
        """)
        self.gpu_info.setWordWrap(True)
        self.gpu_info.setFixedSize(480, 55)

        settings_layout.addWidget(model_label)
        settings_layout.addWidget(self.model_combo)
        settings_layout.addWidget(self.gpu_info)

        parent_layout.addWidget(settings_group)

    def create_progress_section(self, parent_layout):
        """Create compact progress section."""
        progress_group = QGroupBox("Progress")
        progress_group.setFixedHeight(110)  # More compact
        progress_layout = QVBoxLayout(progress_group)
        progress_layout.setSpacing(8)

        # Overall progress - compact
        overall_widget = QWidget()
        overall_widget.setFixedHeight(30)
        overall_layout = QHBoxLayout(overall_widget)
        overall_layout.setContentsMargins(0, 0, 0, 0)
        overall_layout.setSpacing(10)

        overall_label = QLabel("Overall:")
        overall_label.setStyleSheet("font-weight: bold; font-size: 12px; margin: 0px; padding: 0px;")
        overall_label.setFixedWidth(60)

        self.overall_progress = QProgressBar()
        self.overall_progress.setMinimum(0)
        self.overall_progress.setMaximum(100)
        self.overall_progress.setFixedHeight(25)

        overall_layout.addWidget(overall_label)
        overall_layout.addWidget(self.overall_progress)

        # Current file progress - compact
        current_widget = QWidget()
        current_widget.setFixedHeight(30)
        current_layout = QHBoxLayout(current_widget)
        current_layout.setContentsMargins(0, 0, 0, 0)
        current_layout.setSpacing(10)

        current_label = QLabel("Current:")
        current_label.setStyleSheet("font-weight: bold; font-size: 12px; margin: 0px; padding: 0px;")
        current_label.setFixedWidth(60)

        self.current_progress = QProgressBar()
        self.current_progress.setMinimum(0)
        self.current_progress.setMaximum(100)
        self.current_progress.setFixedHeight(25)

        current_layout.addWidget(current_label)
        current_layout.addWidget(self.current_progress)

        # Status - compact
        self.status_label = QLabel("Ready to translate")
        self.status_label.setStyleSheet("""
            background-color: #404040;
            border: 2px solid #555555;
            border-radius: 6px;
            padding: 6px;
            font-weight: bold;
            font-size: 11px;
            margin: 0px;
        """)
        self.status_label.setFixedHeight(35)
        self.status_label.setWordWrap(True)

        progress_layout.addWidget(overall_widget)
        progress_layout.addWidget(current_widget)
        progress_layout.addWidget(self.status_label)

        parent_layout.addWidget(progress_group)

    def create_button_section(self, parent_layout):
        """Create compact button section."""
        button_widget = QWidget()
        button_widget.setFixedHeight(50)  # More compact
        button_layout = QHBoxLayout(button_widget)
        button_layout.setContentsMargins(0, 0, 0, 0)
        button_layout.setSpacing(12)

        # Files info - compact
        self.files_info_label = QLabel("No files selected")
        self.files_info_label.setStyleSheet("font-size: 13px; font-weight: bold; margin: 0px; padding: 0px;")
        self.files_info_label.setFixedWidth(550)

        # Translate button - compact
        self.translate_button = QPushButton("START TRANSLATION")
        self.translate_button.setObjectName("primaryButton")
        self.translate_button.clicked.connect(self.start_translation)
        self.translate_button.setEnabled(False)
        self.translate_button.setFixedSize(180, 40)

        button_layout.addWidget(self.files_info_label)
        button_layout.addWidget(self.translate_button)

        parent_layout.addWidget(button_widget)

    def apply_theme(self):
        """Apply the selected theme."""
        if self.is_dark_theme:
            self.setStyleSheet(ThemeManager.get_dark_theme())
        else:
            self.setStyleSheet(ThemeManager.get_light_theme())

    def toggle_theme(self):
        """Toggle between dark and light theme."""
        self.is_dark_theme = not self.is_dark_theme
        self.settings.setValue("dark_theme", self.is_dark_theme)

        # Update toggle button text
        self.theme_toggle.setText("🌙 Dark" if self.is_dark_theme else "☀️ Light")
        self.theme_toggle.setChecked(self.is_dark_theme)

        # Apply new theme
        self.apply_theme()

    def browse_files(self):
        """Browse for files."""
        audio_ext = "*.mp3 *.wav *.flac *.ogg *.m4a *.aac"
        video_ext = "*.mp4 *.avi *.mkv *.mov *.wmv *.flv *.webm"
        file_filter = f"Media Files ({audio_ext} {video_ext});;All Files (*.*)"

        filenames, _ = QFileDialog.getOpenFileNames(
            self, "Select Files", os.path.abspath("."), file_filter
        )

        if filenames:
            self.add_files(filenames)

    def add_files(self, file_paths):
        """Add files to the list."""
        for file_path in file_paths:
            if file_path not in self.file_paths:
                self.file_paths.append(file_path)
                self.add_file_widget(file_path)
        self.update_files_info()

    def add_file_widget(self, file_path):
        """Add file widget to list."""
        file_item = FileItem(file_path)
        file_item.remove_requested.connect(self.remove_file)

        list_item = QListWidgetItem()
        list_item.setSizeHint(QSize(0, 40))  # Compact size

        self.file_list.addItem(list_item)
        self.file_list.setItemWidget(list_item, file_item)

        self.file_widgets[file_path] = (list_item, file_item)

    def remove_file(self, file_path):
        """Remove file from list."""
        if file_path in self.file_paths:
            self.file_paths.remove(file_path)

            if file_path in self.file_widgets:
                list_item, _ = self.file_widgets[file_path]
                row = self.file_list.row(list_item)
                self.file_list.takeItem(row)
                del self.file_widgets[file_path]

        self.update_files_info()

    def clear_files(self):
        """Clear all files."""
        self.file_paths.clear()
        self.file_widgets.clear()
        self.file_list.clear()
        self.update_files_info()

    def update_files_info(self):
        """Update files info label."""
        count = len(self.file_paths)
        if count == 0:
            self.files_info_label.setText("No files selected")
            self.translate_button.setEnabled(False)
        elif count == 1:
            self.files_info_label.setText("1 file ready for translation")
            self.translate_button.setEnabled(True)
        else:
            self.files_info_label.setText(f"{count} files ready for translation")
            self.translate_button.setEnabled(True)

    def get_selected_model(self):
        """Get selected model."""
        return self.model_combo.currentData()

    def check_gpu(self):
        """Check GPU availability."""
        try:
            import torch

            if torch.cuda.is_available():
                gpu_name = torch.cuda.get_device_name(0)
                gpu_memory = torch.cuda.get_device_properties(0).total_memory / (1024**3)

                gpu_text = f"🚀 GPU: {gpu_name}\n💾 {gpu_memory:.1f}GB VRAM"
                self.gpu_info.setText(gpu_text)
                self.gpu_info.setStyleSheet("""
                    background-color: #2d5a2d;
                    border: 2px solid #4CAF50;
                    border-radius: 8px;
                    padding: 12px;
                    font-size: 12px;
                    font-weight: bold;
                    color: #ffffff;
                """)
            else:
                gpu_text = "⚠️ No GPU Detected\n🐌 Using CPU (slower)"
                self.gpu_info.setText(gpu_text)
                self.gpu_info.setStyleSheet("""
                    background-color: #5a4d2d;
                    border: 2px solid #ff9800;
                    border-radius: 8px;
                    padding: 12px;
                    font-size: 12px;
                    font-weight: bold;
                    color: #ffffff;
                """)

        except Exception as e:
            error_text = f"❌ GPU Error\n{str(e)[:25]}..."
            self.gpu_info.setText(error_text)
            self.gpu_info.setStyleSheet("""
                background-color: #5a2d2d;
                border: 2px solid #f44336;
                border-radius: 8px;
                padding: 12px;
                font-size: 12px;
                font-weight: bold;
                color: #ffffff;
            """)

    def start_translation(self):
        """Start translation process."""
        if not self.file_paths:
            QMessageBox.warning(self, "Warning", "Please select files first.")
            return

        # Disable UI
        self.translate_button.setEnabled(False)
        self.browse_button.setEnabled(False)
        self.clear_button.setEnabled(False)

        # Reset progress
        self.overall_progress.setValue(0)
        self.current_progress.setValue(0)
        self.status_label.setText("Starting translation...")

        # Reset file statuses
        for _, (_, file_item) in self.file_widgets.items():
            file_item.update_status("Waiting")

        # Start worker
        model_size = self.get_selected_model()
        self.worker = TranslationWorker(self.file_paths.copy(), model_size)
        self.worker.progress_updated.connect(self.on_file_progress)
        self.worker.file_complete.connect(self.on_file_complete)
        self.worker.file_error.connect(self.on_file_error)
        self.worker.all_complete.connect(self.on_all_complete)
        self.worker.start()

    def on_file_progress(self, file_path, progress, status):
        """Handle file progress."""
        # Update current progress
        self.current_progress.setValue(int(progress * 100))

        # Update file status
        if file_path in self.file_widgets:
            _, file_item = self.file_widgets[file_path]
            file_item.update_status(f"{int(progress * 100)}%")

        # Update status
        file_name = Path(file_path).name
        if len(file_name) > 25:
            file_name = file_name[:22] + "..."
        self.status_label.setText(f"Processing: {file_name}\n{status}")

        # Update overall progress
        completed = sum(1 for _, (_, fi) in self.file_widgets.items()
                       if fi.status_label.text() in ["Complete", "Error"])
        overall = (completed + progress) / len(self.file_paths)
        self.overall_progress.setValue(int(overall * 100))

    def on_file_complete(self, file_path, output_path):
        """Handle file completion."""
        if file_path in self.file_widgets:
            _, file_item = self.file_widgets[file_path]
            file_item.update_status("Complete")

    def on_file_error(self, file_path, error_message):
        """Handle file error."""
        if file_path in self.file_widgets:
            _, file_item = self.file_widgets[file_path]
            file_item.update_status("Error")

    def on_all_complete(self):
        """Handle all files complete."""
        # Re-enable UI
        self.translate_button.setEnabled(True)
        self.browse_button.setEnabled(True)
        self.clear_button.setEnabled(True)

        # Update progress
        self.overall_progress.setValue(100)
        self.current_progress.setValue(100)
        self.status_label.setText("All translations completed!")

        # Count results
        completed = sum(1 for _, (_, fi) in self.file_widgets.items()
                       if fi.status_label.text() == "Complete")
        errors = sum(1 for _, (_, fi) in self.file_widgets.items()
                    if fi.status_label.text() == "Error")

        # Show message
        if errors == 0:
            QMessageBox.information(self, "Success",
                                  f"All {completed} files translated successfully!\n"
                                  f"SRT files saved in source folders.")
        else:
            QMessageBox.warning(self, "Completed with Errors",
                               f"Completed: {completed}\nErrors: {errors}")

def main():
    """Main function."""
    app = QApplication(sys.argv)
    app.setApplicationName("Audio Translator")
    app.setStyle('Fusion')

    window = AudioTranslatorApp()
    window.show()

    sys.exit(app.exec())

if __name__ == "__main__":
    main()
