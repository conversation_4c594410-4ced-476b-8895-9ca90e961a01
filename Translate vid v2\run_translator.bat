@echo off
echo Audio Translator - Final Clean Version
echo =====================================
echo.

REM Check if virtual environment exists
if exist "venv\Scripts\activate.bat" (
    echo Activating virtual environment...
    call venv\Scripts\activate.bat
) else (
    echo Virtual environment not found. Using system Python...
)

REM Install/update required packages
echo Installing required packages...
python -m pip install --upgrade pip
python -m pip install -r requirements.txt

echo.
echo Starting Audio Translator...
python audio_translator_fixed_ui.py

pause
